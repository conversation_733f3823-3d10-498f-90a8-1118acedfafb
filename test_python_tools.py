#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src import utils
import src.LangGraphUtils as LangGraphUtils
from dotenv import load_dotenv

load_dotenv()

def test_python_tools():
    print("Testing Python tool integration...")
    
    # Load test prompt and markdown
    prompts = utils.load_prompt_sequence("prompts/test_python_tools.yaml")
    markdown = utils.load_markdown("markdowns/test_company.md")
    
    print(f"Loaded {len(prompts)} prompts")
    print(f"Markdown length: {len(markdown)} characters")
    
    # Create workflow and graph
    workflow = utils.create_workflow_state(prompts)
    lg = LangGraphUtils.LangGraphUtils(model="gpt-4o-mini")
    graph = lg.build_graph(prompts, workflow)
    
    print("Running graph with Python tools...")
    initial_state = {"markdown": markdown}
    
    try:
        state_stream = graph.stream(initial_state)
        
        print("Results:")
        for update in state_stream:
            print(update)
            
    except Exception as e:
        print(f"Error during execution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_python_tools()
