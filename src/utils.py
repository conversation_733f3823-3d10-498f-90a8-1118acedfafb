import yaml
from typing import List, Dict, Type, TypedDict, Any
import os
from sqlalchemy import create_engine, Table, Column, String, Text, MetaData, select, insert, BigInteger, TIMESTAMP, func
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import UUID, JSONB

from dotenv import load_dotenv
from pathlib import Path

load_dotenv(Path(__file__).resolve().parent / ".env")

def create_workflow_state(prompts: List[Dict[str, str]]) -> Type[TypedDict]:
    """
    Dynamically create a TypedDict with fields based on prompt names.

    Args:
        prompts: List of prompt dictionaries with 'name' and 'prompt' keys.

    Returns:
        A TypedDict class with fields for each prompt name.
    """
    # Step 1: Required fields TypedDict
    required_fields = {'markdown': str}
    Required = TypedDict('RequiredWorkflowState', required_fields, total=True)

    # Step 2: Optional fields TypedDict (including python_context)
    optional_fields = {step['name']: str for step in prompts}
    optional_fields['python_context'] = dict  # Add python context for tool execution
    Optional = TypedDict('OptionalWorkflowState', optional_fields, total=False)

    # Step 3: Merge via multiple inheritance
    class DynamicWorkflowState(Required, Optional):
        pass

    return DynamicWorkflowState


def load_markdown(markdown_file: str):
    with open(markdown_file, "r") as f:
        return f.read()

def load_prompt_sequence(yaml_file: str):
    with open(yaml_file, 'r') as f:
        return yaml.safe_load(f)

def get_engine():
    url = (
        f"postgresql://{os.getenv('DB_USER')}:{os.getenv('DB_PASSWORD')}"
        f"@{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"
        f"?sslmode={os.getenv('DB_SSLMODE', 'require')}")

    engine = create_engine("postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=require")
#    engine = create_engine(url, pool_pre_ping=True)
    return engine


def load_markdown_db(cib_id, table_name):
    engine = get_engine()
    metadata = MetaData()
    metadata.reflect(bind=engine)
    markdown_table = Table(table_name, metadata, autoload_with=engine)

    with Session(engine) as session:
        # Determine which timestamp column to use (created_at or updated_at)
        timestamp_column = None
        if hasattr(markdown_table.c, 'created_at'):
            timestamp_column = markdown_table.c.created_at
        elif hasattr(markdown_table.c, 'updated_at'):
            timestamp_column = markdown_table.c.updated_at
        else:
            # If no timestamp column found, just get any record
            stmt = select(markdown_table.c.markdown).where(markdown_table.c.base_url == cib_id)
            result = session.execute(stmt).scalar()
            return result

        # Get the latest record by timestamp for the given cib_id
        stmt = (select(markdown_table.c.markdown)
                .where(markdown_table.c.base_url == cib_id)
                .order_by(timestamp_column.desc())
                .limit(1))
        result = session.execute(stmt).scalar()
        return result

def get_urls_from_db():
    engine = get_engine()
    metadata = MetaData()
    documents = Table("companies_new", metadata, Column("base_url", String))

    def fetch_documents():
        with engine.connect() as conn:
            result = conn.execution_options(stream_results=True).execute(
                select(documents.c.base_url)
            )
            for row in result:
                yield row[0]

    return fetch_documents


def create_extraction_runs_table():
    """Create the extraction_runs table if it doesn't exist"""
    engine = get_engine()
    metadata = MetaData()

    # Define the table schema (based on driver.py lines 57-68)
    extraction_runs = Table(
        "extraction_runs",
        metadata,
        Column("id", BigInteger, primary_key=True, autoincrement=True),
        Column("url", Text),
        Column("prompt_id", Text),
        Column("run_id", UUID(as_uuid=True)),
        Column("step", Text, nullable=False),
        Column("answer", JSONB, nullable=False),
        Column("created_at", TIMESTAMP(timezone=True), server_default=func.now()),
    )

    # Create table if it doesn't exist
    metadata.create_all(engine)

    return extraction_runs


def get_extraction_runs_table():
    """Get the extraction_runs table object, creating it if it doesn't exist"""
    engine = get_engine()
    metadata = MetaData()

    # Try to reflect existing table, create if doesn't exist
    try:
        metadata.reflect(bind=engine)
        if 'extraction_runs' in metadata.tables:
            return metadata.tables['extraction_runs']
    except Exception:
        pass

    # Create table if reflection failed or table doesn't exist
    return create_extraction_runs_table()

