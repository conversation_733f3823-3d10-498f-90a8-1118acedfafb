
from typing import List, Dict, Type, TypedDict, Any
import json
from langgraph.graph import StateGraph, E<PERSON>
from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from langchain_core.messages import ToolMessage
import os
from .python_executor import execute_python_code, PYTHON_EXECUTOR_TOOL

# System message for LLM with tool calling capability
SYSTEM_MESSAGE = """You are a helpful assistant that can execute Python code when needed for calculations, data processing, and analysis.

You have access to a Python interpreter tool that you can use for:
- Mathematical calculations (averages, ranges, etc.)
- Counting and list operations
- Data processing and analysis
- Any computational work that would be easier with code

When you need to perform calculations or data processing, use the execute_python_code tool. Available Python modules include: math, datetime, re, json.

Always respond with valid JSON only at the end of your response."""


def make_node(llm, prompt, state_key):
    def node(state: Dict[str, Any]) -> Dict[str, Any]:
        #print(f"Node {state_key} starting - received state with keys: {list(state.keys())}")

        try:
            filled = prompt.format(**state)
            #print(f"Node {state_key} - prompt filled, length: {len(filled)} characters")
        except KeyError as e:
            print(f"Missing key in state: {e}")
            print(f"Available keys: {list(state.keys())}")
            raise

        # Bind tools to the LLM
        llm_with_tools = llm.bind_tools([PYTHON_EXECUTOR_TOOL])

        # Call the LLM with tool capability
        messages = [
            SystemMessage(content=SYSTEM_MESSAGE),
            HumanMessage(content=filled)
        ]

        try:
            #print(f"Node {state_key} - invoking LLM with tools...")
            response = llm_with_tools.invoke(messages)

            # Handle tool calls if present
            while response.tool_calls:
                #print(f"Node {state_key} - processing {len(response.tool_calls)} tool calls")

                for tool_call in response.tool_calls:
                    if tool_call["name"] == "execute_python_code":
                        code = tool_call["args"]["code"]
                        #print(f"Node {state_key} - executing Python code")

                        # Execute the Python code
                        execution_result = execute_python_code(code)

                        # Add tool result to messages
                        messages.append(response)
                        messages.append(ToolMessage(
                            content=json.dumps(execution_result),
                            tool_call_id=tool_call["id"]
                        ))

                # Continue conversation with tool results
                response = llm_with_tools.invoke(messages)

            final_content = response.content
            #print(f"Node {state_key} - received final response, length: {len(str(final_content))} characters")

        except Exception as e:
            print(f"Error invoking LLM for {state_key}: {str(e)}")
            if "timeout" in str(e).lower() or "timed out" in str(e).lower():
                print(f"Timeout occurred for {state_key}. Consider reducing input size or increasing timeout.")
            raise Exception(f"LLM invocation failed for {state_key}: {str(e)}")

        try:
            parsed = json.loads(final_content)
            final_response = parsed
            print(f"Node {state_key} - successfully parsed JSON response")
        except Exception:
            print(f"Node {state_key} - response is not JSON, using as-is")
            final_response = final_content

        print(f"Node {state_key} - completed successfully")

        # Update state with python_context if it was used
        result = {state_key: final_response}
        if 'python_context' not in state:
            result['python_context'] = {}

        return result

    return node

class LangGraphUtils:
    def __init__(self, model="gpt-4o-mini"):
        if model.lower().startswith("gpt"):
            self.llm = ChatOpenAI(model=model, temperature=0.0, api_key=os.getenv("OPEN_AI_API_KEY"))
        else:
            print("Using another model: {model}")
            # self.llm = ChatOpenAI(
            #     model=model,
            #     base_url="https://ollama.synergy-impact.de/v1",
            #     api_key="ollama-test-123456789",
            #     timeout=120,
            #     request_timeout=120,
            #     max_retries=3
            # )
            self.llm = ChatOpenAI(model=model, temperature=0.0,
                                  base_url='https://api.inference.net/v1',
                                  api_key="inference-80e2753f89534ddd9027152d54b17267")

    def build_graph(self, prompts, workflow):
        """Build a LangGraph from a list of prompts"""

        # Create a state graph with our dynamic state type
        sg = StateGraph(workflow)

        # Create a node for each prompt
        for step in prompts:
            step_name = step['name']
            prompt_template = step['prompt']
            node_name = f"{step_name}_node"

            sg.add_node(node_name, make_node(self.llm, prompt_template, step_name))

        # Create edges between nodes
        for i in range(len(prompts) - 1):
            current_node = f"{prompts[i]['name']}_node"
            next_node = f"{prompts[i + 1]['name']}_node"
            sg.add_edge(current_node, next_node)

        # Set entry point and final edge
        first_node = f"{prompts[0]['name']}_node"
        last_node = f"{prompts[-1]['name']}_node"
        sg.add_edge(last_node, END)
        sg.set_entry_point(first_node)

        # Compile the graph
        return sg.compile()


