- name: analyze_employees
  prompt: |
    From the following company description, find employee information and calculate averages:

    1. Look for employee counts or ranges (like "75-125 employees" or "25-30 developers")
    2. For any ranges found, calculate the average using Python
    3. List all employee-related numbers found

    Use the Python interpreter tool to perform calculations when needed.

    Respond with a JSON object like:
    {{
      "employee_ranges_found": [...],  // list of ranges like ["75-125", "25-30"]
      "calculated_averages": [...],    // corresponding averages [100, 27.5]
      "total_employee_estimate": ...,  // best estimate of total employees
      "calculation_details": "..."     // explain your calculations
    }}

    Company description:
    {markdown}
